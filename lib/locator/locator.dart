import 'package:curl_logger_dio_interceptor/curl_logger_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:passkeys/authenticator.dart';
import 'package:toii_mesh/core/repository/auth_repository.dart';
import 'package:toii_mesh/core/repository/user_repository.dart';
import 'package:toii_mesh/core/service/auth_service.dart';
import 'package:toii_mesh/core/service/auth_xmtp_bridge_service.dart';
import 'package:toii_mesh/core/service/pass_key/auth_passkey_service.dart';
import 'package:toii_mesh/core/service/pass_key/local_relying_party_server.dart';
import 'package:toii_mesh/core/service/user_service.dart';
import 'package:toii_mesh/cubit/app_settings/app_settings_cubit.dart';
import 'package:toii_mesh/cubit/auth/delete_account/delete_account_cubit.dart';
import 'package:toii_mesh/cubit/auth/logout/logout_cubit.dart';
import 'package:toii_mesh/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_mesh/cubit/theme/theme_cubit.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_cubit.dart';
import 'package:toii_mesh/flavors/flavors.dart';
import 'package:toii_mesh/utils/interceptor/log_api_intercepter.dart';
import 'package:toii_mesh/utils/interceptor/token_interceptor.dart';
import 'package:toii_mesh/utils/navigation_service.dart';
import 'package:toii_mesh/utils/shared_prefs/shared_prefs.dart';

GetIt serviceLocator = GetIt.instance;
Future setupLocator() async {
  await SharedPref.getInstance();

  ///Setup Navigation Service
  serviceLocator.registerLazySingleton(() => NavigationService());

  serviceLocator.registerLazySingleton(() => ThemeCubit());

  final Dio dio = await setupDio(F.url);
  serviceLocator.registerLazySingleton<Dio>(() => dio);

  /// Service
  serviceLocator.registerLazySingleton(() => AuthService(serviceLocator()));
  serviceLocator.registerLazySingleton(() => UserService(serviceLocator()));

  serviceLocator.registerLazySingleton(
    () => AuthPassKeyService(
      rps: LocalRelyingPartyServer(),
      authenticator: PasskeyAuthenticator(debugMode: kDebugMode),
    ),
  );

  serviceLocator.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(authService: serviceLocator()),
  );

  serviceLocator.registerLazySingleton<UserRepository>(
    () => UserRepositoryImpl(userService: serviceLocator()),
  );

  // final mlxFlutterPlugin = MlxFlutter();
  // serviceLocator.registerLazySingleton(() => mlxFlutterPlugin);

  // cubit
  serviceLocator.registerLazySingleton(
    () => AppSettingsCubit(appSettingsRepository: serviceLocator()),
  );

  serviceLocator.registerLazySingleton(() => ProfileCubit());
  serviceLocator.registerLazySingleton(() => LogoutCubit());
  serviceLocator.registerLazySingleton(() => DeleteAccountCubit());

  // Register XmtpCubit as singleton
  serviceLocator.registerLazySingleton(() => XmtpCubit());

  // Register AuthXmtpBridgeService as singleton
  serviceLocator.registerLazySingleton(() => AuthXmtpBridgeService());

  // Initialize the bridge service after all dependencies are registered
  serviceLocator<AuthXmtpBridgeService>().initialize();
}

Future<Dio> setupDio(String baseUrl) async {
  final options = BaseOptions(
    receiveDataWhenStatusError: true,
    connectTimeout: const Duration(seconds: 60),
    receiveTimeout: const Duration(seconds: 60),
    responseType: ResponseType.json,
    baseUrl: baseUrl,
  );
  final Dio dio = Dio(options);

  dio.interceptors.add(TokenInterceptor());
  dio.interceptors.add(LogAPIInterceptor());

  dio.interceptors.add(CurlLoggerDioInterceptor(printOnSuccess: true));
  dio.interceptors.add(LogInterceptor(requestBody: true, responseBody: true));
  return dio;
}
