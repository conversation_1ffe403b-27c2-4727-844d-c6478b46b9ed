import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_mesh/core/repository/auth_repository.dart';
import 'package:toii_mesh/cubit/auth/logout/logout_cubit.dart';
 

part 'delete_account_state.dart';

class DeleteAccountCubit extends Cubit<DeleteAccountState> {
  DeleteAccountCubit() : super(const DeleteAccountState());

  final AuthRepository _authRepository = GetIt.instance<AuthRepository>();
  final LogoutCubit _logoutCubit = GetIt.instance<LogoutCubit>();

  /// Delete user account by user ID
  Future<void> deleteAccount(String userId) async {
    try {
      emit(state.copyWith(status: DeleteAccountStatus.loading));

      // Call the delete account API
      await _authRepository.deleteAccount(userId);

      emit(
        state.copyWith(
          status: DeleteAccountStatus.success,
          message: 'Account deleted successfully',
          errorMessage: null,
        ),
      );

      // Automatically trigger logout after successful account deletion
      _logoutCubit.logout();
    } on DioException catch (e) {
      emit(
        state.copyWith(
          status: DeleteAccountStatus.failure,
          errorMessage: e.response?.data['message'] ?? e.toString(),
        ),
      );
    } on Exception catch (e) {
      emit(
        state.copyWith(
          status: DeleteAccountStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    }
  }

  /// Reset delete account state to initial
  void resetState() {
    emit(const DeleteAccountState());
  }
}
