name: toii_mesh
description: "A Flutter project implementing Toii social media UI"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.4.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.6
  toii_xmtp_flutter:
    path: plugin/toii_xmtp_flutter
  retrofit:  
  logger: ^2.0.2
  flutter_bloc:  
  equatable: 
  uuid:  
  shared_preferences:  
  curl_logger_dio_interceptor:
  url_launcher:
  pinput:
  dio:
  json_annotation:
  flutter_secure_storage:
  flutter_easyloading: ^3.0.5
  passkeys: ^2.13.0
  crystal_navigation_bar:
  device_info_plus:
  shimmer: ^3.0.0
  cached_network_image: ^3.4.1
  get_it: ^8.2.0
  go_router: ^16.2.2
  intl:  
  vector_graphics: ^1.1.19
  flutter_svg: 
  lottie: ^3.3.1
  flutter_localizations:
    sdk: flutter
  web3dart: ^2.7.3
  hex: ^0.2.0
  bip39:
  bip32:
  encrypt:
  flutter_chat_ui: ^1.6.15
  flutter_chat_types: ^3.6.2
 
   

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

  build_runner: ^2.4.6
  json_serializable: ^6.7.1
  retrofit_generator:
  flutter_gen_runner:
  intl_utils:
 
flutter_intl:
  enabled: true
  main_locale: en
flutter:

  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/json/
    - assets/defibrowser/
    - assets/abi/
  fonts:
    - family: Manrope
      fonts:
        - asset: assets/fonts/Manrope-Bold.ttf
          weight: 700
        - asset: assets/fonts/Manrope-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Manrope-Medium.ttf
          weight: 500
        - asset: assets/fonts/Manrope-Regular.ttf
          weight: 400
        - asset: assets/fonts/Manrope-Light.ttf
          weight: 300

flutter_gen:
  output: lib/gen/
  line_length: 80
  integrations:
    flutter_svg: true
